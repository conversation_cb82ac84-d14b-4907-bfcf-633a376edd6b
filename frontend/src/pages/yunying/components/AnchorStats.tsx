/**
 * 主播统计组件
 * 
 * 展示主播的详细业务统计数据
 */

import React, { useState } from 'react'
import { Calendar, TrendingUp, TrendingDown, Minus, RefreshCw } from 'lucide-react'
import { addDays, startOfDay, endOfDay } from 'date-fns'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge, Skeleton } from '@/components/ui'
import DateRangePicker from '@/components/ui/date-range-picker'
import type { DateRange } from 'react-day-picker'
import { useAnchorStats, useStatsFormatter } from '../hooks/useAnchorStats'
import { OperationsUtils } from '@/services/operations'
import type { AnchorListResponse } from '../types/operations'
import { PermissionStatsCard } from '@/components/operations/PermissionStatsCard'


/**
 * 主播统计组件属性
 */
interface AnchorStatsProps {
  /** 选中的主播 */
  anchor: AnchorListResponse | null
  /** 返回主播列表回调 */
  onBack?: () => void
}

/**
 * 统计卡片属性
 */
interface StatsCardProps {
  title: string
  value: string
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  trendValue?: string
  description?: string
  loading?: boolean
}

/**
 * 统计卡片组件
 */
function StatsCard({ title, value, unit, trend, trendValue, description, loading }: StatsCardProps) {
  if (loading) {
    return (
      <Card>
        <CardContent className="p-4">
          <Skeleton className="h-4 w-20 mb-2" />
          <Skeleton className="h-8 w-24 mb-1" />
          <Skeleton className="h-3 w-16" />
        </CardContent>
      </Card>
    )
  }
  
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-500" />
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-500" />
      default:
        return <Minus className="h-3 w-3 text-gray-500" />
    }
  }
  
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          {trend && trendValue && (
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className="text-xs text-muted-foreground">{trendValue}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-baseline space-x-1">
          <span className="text-2xl font-bold">{value}</span>
          {unit && <span className="text-sm text-muted-foreground">{unit}</span>}
        </div>
        
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 主播统计组件
 */
export function AnchorStats({ anchor, onBack }: AnchorStatsProps) {
  // 设置默认时间范围为最近30天
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => ({
    from: startOfDay(addDays(new Date(), -29)),
    to: endOfDay(new Date()),
  }))
  
  const {
    statsData,
    firstRechargeData,
    statsLoading,
    firstRechargeLoading,
    error,
    loadAllStats
  } = useAnchorStats()
  
  const { formatStatsCards, formatFirstRechargeCards } = useStatsFormatter()
  
  /**
   * 处理时间范围变化
   */
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
    
    if (anchor && range?.from && range?.to) {
      const startTime = OperationsUtils.dateToTimestamp(range.from)
      const endTime = OperationsUtils.dateToTimestamp(range.to)
      loadAllStats(anchor.id, startTime, endTime)
    } else if (anchor) {
      loadAllStats(anchor.id)
    }
  }
  
  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    if (anchor) {
      // 使用当前时间范围或默认的30天范围
      const effectiveDateRange = dateRange || {
        from: startOfDay(addDays(new Date(), -29)),
        to: endOfDay(new Date()),
      }

      if (effectiveDateRange.from && effectiveDateRange.to) {
        const startTime = OperationsUtils.dateToTimestamp(effectiveDateRange.from)
        const endTime = OperationsUtils.dateToTimestamp(effectiveDateRange.to)
        loadAllStats(anchor.id, startTime, endTime)
      }
    }
  }
  
  // 当主播变化时加载数据
  React.useEffect(() => {
    if (anchor) {
      // 总是使用时间范围，如果没有设置则使用默认的30天范围
      const effectiveDateRange = dateRange || {
        from: startOfDay(addDays(new Date(), -29)),
        to: endOfDay(new Date()),
      }

      if (effectiveDateRange.from && effectiveDateRange.to) {
        const startTime = OperationsUtils.dateToTimestamp(effectiveDateRange.from)
        const endTime = OperationsUtils.dateToTimestamp(effectiveDateRange.to)
        loadAllStats(anchor.id, startTime, endTime)
      }
    }
  }, [anchor, loadAllStats])
  
  if (!anchor) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        请选择一个主播查看统计数据
      </div>
    )
  }
  
  const statsCards = formatStatsCards(statsData)
  const firstRechargeCards = formatFirstRechargeCards(firstRechargeData)
  
  return (
    <div className="space-y-6">
      {/* 头部信息 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              返回列表
            </Button>
          )}
          <div>
            <h2 className="text-xl font-semibold">{anchor.nickname} 的统计数据</h2>
            <p className="text-sm text-muted-foreground">
              用户名: {anchor.username} | 邀请码: {anchor.inviteCode}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={statsLoading || firstRechargeLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(statsLoading || firstRechargeLoading) ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>
      
      {/* 主播基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">基本信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">状态:</span>
              <Badge 
                variant={anchor.state === 1 ? 'default' : 'destructive'}
                className="ml-2"
              >
                {OperationsUtils.getUserStateText(anchor.state)}
              </Badge>
            </div>
            <div>
              <span className="text-muted-foreground">身份:</span>
              <Badge variant="outline" className="ml-2">
                {OperationsUtils.getUserIdentityText(anchor.identity)}
              </Badge>
            </div>
            <div>
              <span className="text-muted-foreground">实名认证:</span>
              <Badge 
                variant={anchor.isauth === 1 ? 'default' : 'secondary'}
                className="ml-2"
              >
                {OperationsUtils.getAuthStatusText(anchor.isauth)}
              </Badge>
            </div>
            <div>
              <span className="text-muted-foreground">下级用户:</span>
              <span className="ml-2 font-medium">{anchor.subUserCount} 人</span>
            </div>
            <div>
              <span className="text-muted-foreground">当前余额:</span>
              <span className="ml-2 font-medium">{OperationsUtils.formatAmount(anchor.coin)} 元</span>
            </div>
            <div>
              <span className="text-muted-foreground">钥匙数量:</span>
              <span className="ml-2 font-medium">{OperationsUtils.formatAmount(anchor.key)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">用户等级:</span>
              <span className="ml-2 font-medium">Lv.{anchor.level}</span>
            </div>
            <div>
              <span className="text-muted-foreground">注册时间:</span>
              <span className="ml-2">{OperationsUtils.formatTimestamp(anchor.createTime)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* 业务统计数据 */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">业务统计</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">时间范围:</span>
            </div>
            <DateRangePicker
              onDateChange={handleDateRangeChange}
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {statsCards.map((card, index) => (
            <PermissionStatsCard
              key={index}
              title={card.title}
              value={card.value}
              unit={card.unit}
              description={card.description}
              trend={card.trend ? {
                value: card.trendValue || '',
                label: card.trend === 'up' ? '增长' : card.trend === 'down' ? '下降' : '稳定',
                direction: card.trend as "up" | "down" | "stable"
              } : undefined}
              permissions={card.permissions}
              noPermissionBehavior={card.noPermissionBehavior}
              noPermissionMessage={card.noPermissionMessage}
              loading={statsLoading}
            />
          ))}
        </div>
      </div>
      
      {/* 首充统计数据 */}
      <div>
        <h3 className="text-lg font-semibold mb-4">首充统计</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {firstRechargeCards.map((card, index) => (
            <StatsCard
              key={index}
              title={card.title}
              value={card.value}
              unit={card.unit}
              trend={card.trend as "up" | "down" | "stable" | undefined}
              trendValue={card.trendValue}
              description={card.description}
              loading={firstRechargeLoading}
            />
          ))}
        </div>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <Card>
          <CardContent className="p-4">
            <div className="text-center text-red-500">
              {error}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* 时间范围提示 */}
      {(() => {
        const effectiveDateRange = dateRange || {
          from: startOfDay(addDays(new Date(), -29)),
          to: endOfDay(new Date()),
        }
        return (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>
                    当前统计时间范围: {effectiveDateRange.from?.toLocaleDateString()} - {effectiveDateRange.to?.toLocaleDateString()}
                  </span>
                </div>
                <div className="text-muted-foreground">
                  <span>所有统计数据均基于此时间范围计算</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })()}
    </div>
  )
}
