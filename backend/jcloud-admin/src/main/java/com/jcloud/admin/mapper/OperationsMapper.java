package com.jcloud.admin.mapper;

import com.jcloud.admin.dto.request.*;
import com.jcloud.admin.dto.response.*;
import com.jcloud.common.annotation.DataSource;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运营数据Mapper接口
 * 使用从库数据源查询vimbox数据库
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
@DataSource("slave")
public interface OperationsMapper {
    

    
    /**
     * 查询主播列表（手动分页）
     * 支持按昵称、状态等条件筛选
     *
     * @param query 查询条件（包含分页参数）
     * @return 主播列表
     */
    List<AnchorListResponse> selectAnchorList(@Param("query") AnchorQueryRequest query);

    /**
     * 统计主播列表总数
     * 支持按昵称、状态等条件筛选
     *
     * @param query 查询条件
     * @return 总记录数
     */
    Long countAnchorList(@Param("query") AnchorQueryRequest query);
    
    /**
     * 查询主播下级用户（手动分页）
     *
     * @param anchorId 主播ID
     * @param query 查询条件（包含分页参数）
     * @return 下级用户列表
     */
    List<SubUserResponse> selectSubUsers(
        @Param("anchorId") Integer anchorId,
        @Param("query") SubUserQueryRequest query
    );

    /**
     * 统计主播下级用户总数
     *
     * @param anchorId 主播ID
     * @param query 查询条件
     * @return 总记录数
     */
    Long countSubUsers(
        @Param("anchorId") Integer anchorId,
        @Param("query") SubUserQueryRequest query
    );
    
    /**
     * 查询用户消费详情（手动分页）
     *
     * @param userId 用户ID
     * @param query 查询条件（包含分页参数）
     * @return 消费详情列表
     */
    List<ConsumeDetailResponse> selectConsumeDetails(
        @Param("userId") Integer userId,
        @Param("query") ConsumeQueryRequest query
    );

    /**
     * 统计用户消费详情总数
     *
     * @param userId 用户ID
     * @param query 查询条件
     * @return 总记录数
     */
    Long countConsumeDetails(
        @Param("userId") Integer userId,
        @Param("query") ConsumeQueryRequest query
    );

    /**
     * 查询用户充值详情（手动分页）
     *
     * @param userId 用户ID
     * @param query 查询条件（包含分页参数）
     * @return 充值详情列表
     */
    List<RechargeDetailResponse> selectRechargeDetails(
        @Param("userId") Integer userId,
        @Param("query") RechargeQueryRequest query
    );

    /**
     * 统计用户充值详情总数
     *
     * @param userId 用户ID
     * @param query 查询条件
     * @return 总记录数
     */
    Long countRechargeDetails(
        @Param("userId") Integer userId,
        @Param("query") RechargeQueryRequest query
    );

    // ==================== 运营统计查询方法 ====================

    /**
     * 获取活跃主播数（本月有业务的主播）
     * 统计在指定时间范围内有充值或消费记录的主播数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxLevel 最大查询层数（基于用户角色权限）
     * @return 活跃主播数量
     */
    Long getActiveAnchorsCount(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("maxLevel") Integer maxLevel
    );

    /**
     * 获取总用户数（所有主播的下级用户总数）
     *
     * @param maxLevel 最大查询层数（基于用户角色权限）
     * @return 总用户数量
     */
    Long getTotalUsersCount(@Param("maxLevel") Integer maxLevel);

    /**
     * 获取新增主播数
     * 统计在指定时间范围内注册的主播数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 新增主播数量
     */
    Long getNewAnchorsCount(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    /**
     * 获取新增用户数
     * 统计在指定时间范围内注册的用户数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxLevel 最大查询层数（基于用户角色权限）
     * @return 新增用户数量
     */
    Long getNewUsersCount(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("maxLevel") Integer maxLevel
    );

    /**
     * 获取总充值金额
     * 统计在指定时间范围内的总充值金额
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxLevel 最大查询层数（基于用户角色权限）
     * @return 总充值金额
     */
    BigDecimal getTotalRechargeAmount(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("maxLevel") Integer maxLevel
    );

    /**
     * 获取总消费金额
     * 统计在指定时间范围内的总消费金额
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param maxLevel 最大查询层数（基于用户角色权限）
     * @return 总消费金额
     */
    BigDecimal getTotalConsumeAmount(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("maxLevel") Integer maxLevel
    );

}
