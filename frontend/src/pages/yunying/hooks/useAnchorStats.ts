/**
 * 主播统计数据管理Hook
 * 
 * 提供主播统计数据和首充统计数据的查询功能
 */

import { useState, useCallback, useEffect } from 'react'
import { toast } from '@/hooks'
import { OperationsService } from '@/services/operations'
import type { AnchorStatsResponse, FirstRechargeStatsResponse } from '../types/operations'
import { PERMISSIONS } from '@/constants/permissions'

/**
 * 主播统计Hook状态接口
 */
interface UseAnchorStatsState {
  /** 主播统计数据 */
  statsData: AnchorStatsResponse | null
  /** 首充统计数据 */
  firstRechargeData: FirstRechargeStatsResponse | null
  /** 统计数据加载状态 */
  statsLoading: boolean
  /** 首充数据加载状态 */
  firstRechargeLoading: boolean
  /** 错误信息 */
  error: string | null
  /** 当前主播ID */
  anchorId: number | null
  /** 时间范围 */
  timeRange: {
    startTime?: number
    endTime?: number
  }
}

/**
 * 主播统计Hook返回值接口
 */
interface UseAnchorStatsReturn extends UseAnchorStatsState {
  /** 加载主播统计数据 */
  loadStats: (anchorId: number, startTime?: number, endTime?: number) => Promise<void>
  /** 加载首充统计数据 */
  loadFirstRechargeStats: (anchorId: number, startTime?: number, endTime?: number) => Promise<void>
  /** 同时加载所有统计数据 */
  loadAllStats: (anchorId: number, startTime?: number, endTime?: number) => Promise<void>
  /** 刷新统计数据 */
  refreshStats: () => Promise<void>
  /** 设置时间范围 */
  setTimeRange: (startTime?: number, endTime?: number) => void
  /** 重置数据 */
  reset: () => void
}

/**
 * 主播统计数据管理Hook
 * 
 * @param initialAnchorId 初始主播ID
 * @param initialTimeRange 初始时间范围
 * @returns 主播统计状态和操作方法
 */
export function useAnchorStats(
  initialAnchorId?: number,
  initialTimeRange?: { startTime?: number; endTime?: number }
): UseAnchorStatsReturn {
  
  const [state, setState] = useState<UseAnchorStatsState>({
    statsData: null,
    firstRechargeData: null,
    statsLoading: false,
    firstRechargeLoading: false,
    error: null,
    anchorId: initialAnchorId || null,
    timeRange: initialTimeRange || {}
  })
  
  /**
   * 加载主播统计数据
   */
  const loadStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      statsLoading: true, 
      error: null,
      anchorId,
      timeRange: { startTime, endTime }
    }))
    
    try {
      const data = await OperationsService.getAnchorStats(anchorId, startTime, endTime)
      
      setState(prev => ({
        ...prev,
        statsData: data,
        statsLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取主播统计数据失败'
      setState(prev => ({
        ...prev,
        statsData: null,
        statsLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [])
  
  /**
   * 加载首充统计数据
   */
  const loadFirstRechargeStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      firstRechargeLoading: true, 
      error: null,
      anchorId,
      timeRange: { startTime, endTime }
    }))
    
    try {
      const data = await OperationsService.getFirstRechargeStats(anchorId, startTime, endTime)
      
      setState(prev => ({
        ...prev,
        firstRechargeData: data,
        firstRechargeLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取首充统计数据失败'
      setState(prev => ({
        ...prev,
        firstRechargeData: null,
        firstRechargeLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [])
  
  /**
   * 同时加载所有统计数据
   */
  const loadAllStats = useCallback(async (
    anchorId: number,
    startTime?: number,
    endTime?: number
  ) => {
    setState(prev => ({ 
      ...prev, 
      statsLoading: true,
      firstRechargeLoading: true,
      error: null,
      anchorId,
      timeRange: { startTime, endTime }
    }))
    
    try {
      const [statsData, firstRechargeData] = await Promise.all([
        OperationsService.getAnchorStats(anchorId, startTime, endTime),
        OperationsService.getFirstRechargeStats(anchorId, startTime, endTime)
      ])
      
      setState(prev => ({
        ...prev,
        statsData,
        firstRechargeData,
        statsLoading: false,
        firstRechargeLoading: false
      }))
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取统计数据失败'
      setState(prev => ({
        ...prev,
        statsData: null,
        firstRechargeData: null,
        statsLoading: false,
        firstRechargeLoading: false,
        error: errorMessage
      }))
      toast({
        title: errorMessage,
        variant: 'destructive'
      })
    }
  }, [])
  
  /**
   * 刷新统计数据
   */
  const refreshStats = useCallback(async () => {
    if (!state.anchorId) return
    
    await loadAllStats(
      state.anchorId,
      state.timeRange.startTime,
      state.timeRange.endTime
    )
  }, [state.anchorId, state.timeRange, loadAllStats])
  
  /**
   * 设置时间范围
   */
  const setTimeRange = useCallback((startTime?: number, endTime?: number) => {
    setState(prev => ({
      ...prev,
      timeRange: { startTime, endTime }
    }))
  }, [])
  
  /**
   * 重置数据
   */
  const reset = useCallback(() => {
    setState({
      statsData: null,
      firstRechargeData: null,
      statsLoading: false,
      firstRechargeLoading: false,
      error: null,
      anchorId: null,
      timeRange: {}
    })
  }, [])
  
  // 当主播ID或时间范围变化时自动加载数据
  useEffect(() => {
    if (state.anchorId) {
      loadAllStats(
        state.anchorId,
        state.timeRange.startTime,
        state.timeRange.endTime
      )
    }
  }, [state.anchorId, state.timeRange.startTime, state.timeRange.endTime, loadAllStats])
  
  return {
    ...state,
    loadStats,
    loadFirstRechargeStats,
    loadAllStats,
    refreshStats,
    setTimeRange,
    reset
  }
}

/**
 * 统计数据格式化Hook
 * 
 * 提供统计数据的格式化和计算功能
 */
export function useStatsFormatter() {
  
  /**
   * 格式化统计卡片数据
   */
  const formatStatsCards = useCallback((stats: AnchorStatsResponse | null) => {
    if (!stats) return []
    
    return [
      {
        title: '区间充值金额',
        value: OperationsUtils.formatAmount(stats.periodTotalRecharge),
        unit: '元',
        description: `历史总充值: ${OperationsUtils.formatAmount(stats.totalRecharge)}元`,
        trend: stats.periodTotalRecharge > 0 ? 'up' : 'stable',
        trendValue: `区间内充值`
      },
      {
        title: '总消费金额',
        value: OperationsUtils.formatAmount(stats.totalConsume),
        unit: '元',
        description: '电能订单 + 钥匙订单'
      },
      {
        title: '用户总数',
        value: stats.userCount.toString(),
        unit: '人',
        trend: stats.periodNewUserCount > 0 ? 'up' : 'stable',
        trendValue: stats.periodNewUserCount.toString()
      },
      {
        title: '新增人数',
        value: (stats.periodNewInviteCount || 0).toString(),
        unit: '人',
        description: '时间区间内新邀请的用户',
        trend: (stats.periodNewInviteCount || 0) > 0 ? 'up' : 'stable',
        trendValue: `+${stats.periodNewInviteCount || 0}`
      },
      {
        title: '实际利润',
        value: OperationsUtils.formatAmount(stats.actualProfit),
        unit: '元',
        description: `利润比: ${OperationsUtils.formatPercentage(stats.profitRatio)}`,
        permissions: [PERMISSIONS.OPERATIONS.STATS.VIEW_PROFIT],
        noPermissionBehavior: 'mask' as const,
        noPermissionMessage: '无权限查看利润数据'
      },
      {
        title: '总流水',
        value: OperationsUtils.formatAmount(stats.totalTurnover),
        unit: '元'
      },
      {
        title: '待发货金额',
        value: OperationsUtils.formatAmount(stats.totalClaimAmount),
        unit: '元',
        permissions: [PERMISSIONS.OPERATIONS.STATS.VIEW_PENDING_AMOUNT],
        noPermissionBehavior: 'mask' as const,
        noPermissionMessage: '无权限查看待发货数据'
      },
      {
        title: '实际发货金额',
        value: OperationsUtils.formatAmount(stats.totalShippedAmount),
        unit: '元',
        permissions: [PERMISSIONS.OPERATIONS.STATS.VIEW_SHIPPED_AMOUNT],
        noPermissionBehavior: 'mask' as const,
        noPermissionMessage: '无权限查看发货数据'
      },
      {
        title: '背包总价值',
        value: OperationsUtils.formatAmount(stats.totalBackpackAmount),
        unit: '元',
        permissions: [PERMISSIONS.OPERATIONS.STATS.VIEW_BACKPACK_VALUE],
        noPermissionBehavior: 'mask' as const,
        noPermissionMessage: '无权限查看背包数据'
      }
    ]
  }, [])
  
  /**
   * 格式化首充统计卡片数据
   */
  const formatFirstRechargeCards = useCallback((stats: FirstRechargeStatsResponse | null) => {
    if (!stats) return []
    
    return [
      {
        title: '首充用户数',
        value: stats.firstRechargeUserCount.toString(),
        unit: '人',
        trend: stats.periodFirstRechargeUserCount > 0 ? 'up' : 'stable',
        trendValue: stats.periodFirstRechargeUserCount.toString()
      },
      {
        title: '首充转化率',
        value: OperationsUtils.formatPercentage(stats.firstRechargeConversionRate),
        description: `${stats.firstRechargeUserCount}/${stats.totalSubUserCount}`
      },
      {
        title: '首充总金额',
        value: OperationsUtils.formatAmount(stats.totalFirstRechargeAmount),
        unit: '元',
        trend: stats.periodFirstRechargeAmount > 0 ? 'up' : 'stable',
        trendValue: OperationsUtils.formatAmount(stats.periodFirstRechargeAmount)
      },
      {
        title: '平均首充金额',
        value: OperationsUtils.formatAmount(stats.avgFirstRechargeAmount),
        unit: '元',
        description: `时间区间内: ${OperationsUtils.formatAmount(stats.periodAvgFirstRechargeAmount)}元`
      }
    ]
  }, [])
  
  return {
    formatStatsCards,
    formatFirstRechargeCards
  }
}
